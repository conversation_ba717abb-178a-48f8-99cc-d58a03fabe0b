
import pytest
from s3path import S3Path

from app.flows.fill_columns import fill_columns
from app.utils.app_config import app_config


class TestFillColumns:
    @pytest.mark.asyncio
    async def test_fill_material(self):
        input_dir = S3Path('/xconfig/tests/fill_columns')
        output_dir = app_config.project_dir / 'tests' / 'outputs' / 'fill_columns'

        list_files = input_dir.glob('*.xlsx')
        list_files = [ input_dir / 'BD_MATERIAL-2.xlsx']
        for input_file in list_files:
            await fill_columns(input_file, output_dir / input_file.name)
