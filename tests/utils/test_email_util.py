from pathlib import Path

import pytest

from app.utils.credential_util import get_credential, CredentialType, GeneralCredential


@pytest.mark.asyncio
async def test_send_email():
    from app.utils.email_util import send_email
    credential = await get_credential(CredentialType.General, 'EMAIL')

    send_email(credential, '<EMAIL>', f'长城送货单 {'xx2'}', f"""
    <html>
  <body>
    <p>{'发货通知单号'} {'客户名称'}</p>
  </body>
</html>
""", cc_addrs='<EMAIL>',
               attachments=[Path('/Users/<USER>/code/fast-flow/.venv/jcache/SZ-STOA2507/SZ-STOA25074332_no.pdf')])