import enum
from pathlib import Path
from typing import Optional, Union

from pydantic import BaseModel, model_validator, Field, field_serializer, field_validator
from s3path import S3Path

from app.utils.app_config import app_config


class OutputType(enum.StrEnum):
    FILE = "file"
    MESSAGE = "message"

class MessageOutputType(enum.IntEnum):
    PROGRESS = 1
    INFO = 2
    WARNING = 8
    ERROR = 4


# 文件输出模型
class FileOutput(BaseModel):
    type: OutputType = OutputType.FILE
    remote: bool = False
    file_path: Path
    content_type: str = "application/octet-stream"
    message: Optional[str] = None
    url: str = None

    @model_validator(mode='before')
    @classmethod
    def validate_file_path(cls, data: dict):
        remote = data.get('remote')
        file_path = data.get('file_path')

        if remote is True  and isinstance(file_path, str):
            data['file_path'] = S3Path(file_path)
        return data

    @field_serializer('file_path')
    def serialize_file_path(self, v: Path) -> str:
        if isinstance(v, S3Path):
            return str(v)
        return str(v.relative_to(app_config.cache_dir))

    @model_validator(mode='after')
    def set_remote(self):
        if isinstance(self.file_path, S3Path):
            self.remote = True
        if self.remote:
            self.url = self.file_path.get_presigned_url()
        return self

# 消息输出模型
class MessageOutput(BaseModel):
    type: OutputType = OutputType.MESSAGE
    message: str
    level: MessageOutputType = MessageOutputType.INFO
    progress: Optional[float] = Field(None, ge=0.0, le=100)  # 可选字段，在0-100范围内
    details: Optional[str] = None



WorkflowOutput = Union[FileOutput, MessageOutput]