import logging
logging.basicConfig(level=logging.INFO)

# load aws credentials from .env
import dotenv
dotenv.load_dotenv()

from .utils.exception_util import set_exception_handler

from fastapi import FastAPI

from .routers import flows
from .routers import files
from .routers import kingd
from app.routers import users
from fastapi.middleware.cors import CORSMiddleware





app = FastAPI()


app.add_middleware(CORSMiddleware, allow_origins=["*"], allow_methods=["*"], allow_headers=["*"], expose_headers=["*"], max_age=86400)


set_exception_handler(app)

app.include_router(flows.router, prefix="/flows", tags=["Flow"])

app.include_router(files.router, prefix="/files", tags=["Files"])

app.include_router(kingd.router, prefix="/kingd", tags=["Kingdee"])

app.include_router(users.router, prefix="/users", tags=["Users"])