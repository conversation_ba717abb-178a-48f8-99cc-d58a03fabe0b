from datetime import datetime

import polars as pl
from fastapi import HTTPException

from app.utils.credential_util import get_credential, CredentialType
from app.schemas.kingd import KDDocumentStatus
from app.utils.play_api import play_api
from .delivery_notice_strategy import DeliveryNoticeStrategy, DeliveryNoticeFactory


class XieKeYunStrategy(DeliveryNoticeStrategy):

    def check(self):
        doc_status = self.outstock_info['DocumentStatus']
        if doc_status != KDDocumentStatus.Auditing:
            raise HTTPException(status_code=400, detail=f'订单状态不是审核中，当前：{KDDocumentStatus(doc_status).description}')

    async def run(self):
        self.check()
        uid = self.user.name
        credential = await get_credential(CredentialType.XieKeYun, uid)
        if not credential:
            raise HTTPException(status_code=400, detail=f'请先配置携客云账号: {uid}')
        return await play_api.post('/xiekeyun/delivery_notice', json = {
            'outstock_info': self.outstock_info,
            'credential': credential,
        })

__xiekeyun_customers = ['SZ-K18030060', 'SZ-K21090004', 'SZ-K15100002', 'SZ-K16050005', 'SZ-K21010200', 'SZ-K18090053']
for customer_no in __xiekeyun_customers:
    DeliveryNoticeFactory.register_strategy(customer_no, XieKeYunStrategy)

