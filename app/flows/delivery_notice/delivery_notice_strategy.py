import logging
from pathlib import Path
from typing import Protocol

import jmespath
import polars as pl
from fastapi import HTTPException

from s3path import S3Path

from app.routers.kingd import form_query, KDQueryModel
from app.schemas.flows import FileOutput
from app.utils.auth_util import UserInfo
from app.utils.file_util import ensure_dir_for_file
from app.utils.kingd_util import KDFilterCondition, KDFilterCompare
from app.utils.wms_api import get_wms_data

from app.utils.app_config import app_config


logger = logging.getLogger(__name__)


class DeliveryNoticeStrategy(Protocol):
    outstock_info: dict
    user: UserInfo
    material_cols = 'FIncreaseQty,F_PYZU_YCXH,F_PYZU_PINGPAI.FName,FName'.split(',')
    dependency_cols = []
    save_remote = False

    dfs: list[pl.DataFrame] = None # 多订单支持

    def __init__(self, outstock_info: dict, user: UserInfo):
        self.outstock_info = self.filter_json(outstock_info)
        self.user = user

    @property
    def cache_folder(self):
        bill_no = self.outstock_info['BillNo']
        cache_folder = bill_no[3:11]
        return app_config.cache_dir / cache_folder

    @property
    def excel_path(self):
        bill_no = self.outstock_info['BillNo']
        xlsx_path = self.cache_folder / f'{bill_no}.xlsx'
        ensure_dir_for_file(xlsx_path)
        return xlsx_path

    @property
    def column_mapping(self) -> dict:
        pass
    @property
    def need_columns(self) -> list:
        return list(self.column_mapping.keys()) + self.dependency_cols

    def has_columns(self, check_columns: list[str]):
        return set(self.need_columns) & set(check_columns)

    def df_columns_pick(self, df: pl.DataFrame) -> pl.DataFrame:
        newdf = df.select([
            pl.col(original).alias(new_name)
            if original in df.columns
            else pl.lit(None).alias(new_name)
            for original, new_name in self.column_mapping.items()
        ])
        return newdf

    def filter_json(self, outstock_json: dict):
        info = jmespath.search("""{
            Id: Id, 
            BillNo: BillNo,
            DocumentStatus: DocumentStatus, 
            SaleOrgNo: SaleOrgId.Number,
            F_PYZU_SDate: F_PYZU_SDate,
            F_BRQ_AllQty: F_BRQ_AllQty,
            customer_name: CustomerID.Name[0].Value, 
            customer_no: CustomerID.Number,
            address_name: HeadLocId.Name,
            CarriageNO: CarriageNO,
            CarriageName: CarrierID.Name[0].Value,
            details: sort_by(SAL_DELIVERYNOTICEENTRY[*].{Id: Id, Seq: Seq, Qty: Qty, F_PYZU_KHDDH: F_PYZU_KHDDH, NoteEntry: NoteEntry, F_PYZU_KHWLH: F_PYZU_KHWLH, F_PYZU_KHZL1: F_PYZU_KHZL1, F_PYZU_KHZL2: F_PYZU_KHZL2, F_PYZU_KHZL3: F_PYZU_KHZL3, "KHSXWL.YCXH": F_PYZU_KHSXWL.F_PYZU_YCXH, "KHSXWL.PINGPAI": F_PYZU_KHSXWL.F_PYZU_PINGPAI.Name[0].Value, "KHSXWL.Name": F_PYZU_KHSXWL.Name[0].Value, OrderSeq: OrderSeq}, &Seq)
            }""", outstock_json)
        """
        OrderSeq 银河表计
        """
        # print(info)
        if logger.isEnabledFor(logging.INFO):
            with pl.Config(tbl_cols=-1, tbl_rows=-1, tbl_width_chars=1000):
                print(pl.DataFrame(info['details']))
        return  info


    async def get_wms_df(self):
        wms_data = await get_wms_data('QuerySerialNo', {'OutStockNo': self.outstock_info['BillNo']})

        if not ( wms_data and wms_data[0]['OutstockNo'] ):
            raise Exception(f'WMS 未生成出库流水: {self.outstock_info["BillNo"]}')

        wms_df = pl.from_records(wms_data, infer_schema_length=None)
        return wms_df

    def get_delivery_notice_df(self):
        return pl.from_records(self.outstock_info['details'])

    async def get_material_info(self, material_nos: list[str]):
        material_nos = ','.join(material_nos)
        return await form_query(KDQueryModel(
            FormId='BD_Material',
            FieldKeys='FMATERIALID, FNumber,' + ','.join(self.material_cols),
            FilterString=[
                KDFilterCondition(FieldName='FNumber', Compare=KDFilterCompare.In, Value=material_nos),
                KDFilterCondition(FieldName='FUseOrgId.FNumber', Value=self.outstock_info['SaleOrgNo'])
            ],
            Limit=1000
        ))

    async def get_merged_df(self) -> pl.DataFrame:
        main_df = await self.get_wms_df()

        # join with detail list from deliverynotice
        df2 = self.get_delivery_notice_df()
        main_df = main_df.with_columns(pl.col('ERPID').cast(pl.Int64)).join(df2, left_on='ERPID', right_on='Id', how='left', suffix='_DN')
        # if self.has_columns(self.material_cols):  # check if material_cols is in column_mapping
        #     ids = main_df['SalesCargoCode'].unique().to_list()
        #     material_info = await self.get_material_info(ids)
        #     df3 = pl.from_records(material_info)
        #     df3 = df3.rename({
        #         "F.PYZU.YCXH": "F_PYZU_YCXH",
        #         "F.PYZU.PINGPAI.FName": "F_PYZU_PINGPAI.FName",
        #     })
        #     main_df = main_df.with_columns(pl.col('SalesCargoCode')).join(df3, left_on='SalesCargoCode', right_on='FNumber', how='left')
        return main_df

    async def process_data(self):
        df = await self.get_merged_df()
        new_df = self.df_columns_pick(df)

        with pl.Config(tbl_cols=-1, tbl_width_chars=1000):
            print(new_df)
        return new_df


    def __upload_excel(self):
        excel_path = self.excel_path
        excel_key = excel_path.relative_to(app_config.cache_dir)
        s3_path = S3Path(f'/{app_config.cache_bucket}/{excel_key}')
        print(f"Uploading {excel_path} to {s3_path}")
        s3_path.write_bytes(excel_path.read_bytes())
        return s3_path

    async def get_excel(self):
        df = await self.process_data()
        df.write_excel(self.excel_path, autofit= True)
        if self.save_remote:
            s3_path = self.__upload_excel()
            return FileOutput(file_path=s3_path)
        return FileOutput(file_path=self.excel_path)
    async def run(self):
        return await self.get_excel()

    async def get_current_df(self) -> pl.DataFrame:
        raise NotImplementedError('多个单号合并必须实现 get_current_df')


class DeliveryNoticeFactory:
    _strategies = {
    }

    @classmethod
    def get_strategy(cls, outstock_json: dict, user: UserInfo) -> DeliveryNoticeStrategy:
        short_info = jmespath.search("""{
            customer_name: CustomerID.Name[0].Value, 
            customer_no: CustomerID.Number
            }""", outstock_json)
        strategy_cls = cls._strategies.get(short_info['customer_no'])
        if not strategy_cls:
            raise HTTPException(status_code=400,
                                detail=f'Unsupported customer: {short_info['customer_name']} {short_info['customer_no']}')
        return strategy_cls(outstock_json, user)

    @classmethod
    def register_strategy(cls, customer_no: str, strategy: DeliveryNoticeStrategy):
        """Register a new customer strategy"""
        cls._strategies[customer_no] = strategy

