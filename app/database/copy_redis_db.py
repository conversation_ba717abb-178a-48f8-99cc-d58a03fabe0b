import redis

from app.utils.app_config import app_config


def migrate_db(from_db: str, to_db: str):
    src_client = redis.Redis.from_url(from_db)
    dest_client = redis.Redis.from_url(to_db)

    # 使用SCAN迭代遍历所有key
    cursor = '0'
    while cursor != 0:
        cursor, keys = src_client.scan(cursor, count=100)  # 分批获取键
        if not keys:
            break

        for key in keys:
            # 获取键的 TTL 和序列化值
            ttl = src_client.pttl(key)  # 以毫秒为单位的 TTL
            serialized_value = src_client.dump(key)
            print(key)

            if ttl == -1:  # -1 表示无过期时间
                ttl = 0

            if serialized_value is not None:
                # 恢复键到目标数据库（支持覆盖）
                dest_client.restore(key, ttl, serialized_value, replace=True)

    print("Database copy completed!")

if __name__ == '__main__':
    redis_url = str(app_config.redis_url).replace('/15', '/')
    migrate_db(redis_url + '0', redis_url + '1')