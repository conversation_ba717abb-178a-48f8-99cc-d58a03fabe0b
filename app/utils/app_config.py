
from pathlib import Path

from pydantic import RedisDsn, ValidationError
from pydantic_settings import BaseSettings

class _AppConfig(BaseSettings):
    model_config = {
        'env_file': '.env',
        # 'extra': 'allow'
    }
    ENV: str = 'production'
    @property
    def is_dev(self):
        return self.ENV == 'dev'

    project_dir: Path = Path(__file__).parent.parent.parent
    cache_dir: Path = project_dir / '.venv' / 'jcache'
    cache_bucket: str = 'flowcache'

    play_api_url: str = 'http://localhost:8602'
    redis_url: RedisDsn
    jwt_secret: str

    kd_api_url: str = 'http://k3c.jeelyton.net/k3cloud'
    kd_account_id: str
    kd_app_id: str
    kd_app_secret: str

    aws_access_key_id: str
    aws_secret_access_key: str
    aws_endpoint_url: str


try:
    app_config = _AppConfig()
except ValidationError as e:
    raise RuntimeError(f"app_config parse error: {e}")

print(repr(app_config))