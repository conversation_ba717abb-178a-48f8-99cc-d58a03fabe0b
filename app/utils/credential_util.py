import enum

from pydantic import BaseModel

from ..database.redis_client import get_str, CachePrefixes, set_str, redis_client
import logging
logger = logging.getLogger(__name__)

class GeneralCredential(BaseModel):
    model_config = {'extra': 'allow'}
    login_id: str
    password: str


class CredentialType(enum.StrEnum):
    General = 'GN'
    KingDee = 'KD'
    XieKeYun = 'XKY'
    ChengCheng004 = 'CC4'
    QiYi = 'QY'



async def get_credential(ctype: CredentialType, uid: str):
    cred = await get_str(f'{CachePrefixes.Credential}:{ctype}:{uid}')
    logger.debug(f'get credential for {ctype}:{uid} is {cred}')
    return cred

async def set_credential(ctype: CredentialType, uid: str, credential: any, **kwargs):
    logger.debug(f'set credential for {ctype}:{uid} to {credential}')
    await set_str(f'{CachePrefixes.Credential}:{ctype}:{uid}', credential, **kwargs)

async def del_credential(ctype: CredentialType, uid: str):
    logger.debug(f'del credential for {ctype}:{uid}')
    await redis_client.delete(f'{CachePrefixes.Credential}:{ctype}:{uid}')